# 常用模式和最佳实践

- AI测试模块一体化重构最佳实践：1)提示词从单文件检查改为双文件对比，忽略页眉页脚差异；2)AI评估服务集成SSE实时推送，支持ai-evaluation-start/complete/error事件；3)设计TDataComparison主表+TDataComparisonStage阶段表的数据库存储方案；4)在ComparisonProgressManager中添加sendAiEvent公开方法；5)修改接口签名传入taskId支持SSE推送。时间戳：2025-07-24T10:37:12+08:00
- AItest前端完全重构最佳实践：1)使用Vue 3 Composition API + TypeScript构建现代化组件架构；2)通过Composables模式(useAITest、useSSEConnection、useMarkdownRenderer)实现逻辑复用；3)集成Pinia Store进行全局状态管理；4)实现Tab页面布局统一管理多个功能模块；5)添加快捷键支持和用户体验优化；6)完善错误处理和状态指示器；7)提供完整的TypeScript类型定义和文档。时间戳：2025-07-29T14:39:19+08:00
