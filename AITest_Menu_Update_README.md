# AI测试菜单数据库修正说明

## 📋 概述

根据AITest前端页面的架构重构，需要对数据库中的菜单配置进行相应修正。重构后的架构采用单一入口模式，所有功能集成在一个页面中，通过模态框提供不同的测试功能。

## 🔄 架构变更对比

### 重构前（旧架构）
```
AI测试 (父菜单)
├── 测试管理 (AITestManagement)
├── API测试 (APITest) 
└── Markdown测试 (MarkdownTest)
```

### 重构后（新架构）
```
AI测试管理 (单一入口)
└── 模态框内包含：
    ├── API接口测试 (Tab页)
    ├── Markdown测试 (Tab页)
    └── 性能测试 (Tab页)
```

## 📁 文件说明

### 1. `update_aitest_menu.sql`
- **用途**: 主要的菜单修正SQL脚本
- **功能**: 
  - 删除旧的多级菜单结构
  - 创建新的单一入口菜单
  - 更新角色权限分配

### 2. `verify_aitest_menu.sql`
- **用途**: 验证修正结果的SQL脚本
- **功能**:
  - 检查菜单是否正确创建
  - 验证旧菜单是否已清理
  - 确认权限分配是否正确

### 3. `execute_aitest_menu_update.bat`
- **用途**: Windows批处理执行脚本
- **功能**: 自动执行SQL修正并显示结果

## 🚀 执行步骤

### 方法一：使用批处理脚本（推荐）
```bash
# 在项目根目录执行
execute_aitest_menu_update.bat
```

### 方法二：手动执行SQL
```bash
# 1. 连接数据库
mysql -h localhost -P 3306 -u root -p123456 k_base

# 2. 执行修正SQL
source update_aitest_menu.sql;

# 3. 验证结果
source verify_aitest_menu.sql;
```

### 方法三：命令行直接执行
```bash
# 执行修正
mysql -h localhost -P 3306 -u root -p123456 k_base < update_aitest_menu.sql

# 验证结果
mysql -h localhost -P 3306 -u root -p123456 k_base < verify_aitest_menu.sql
```

## ✅ 验证检查项

执行完成后，请确认以下检查项：

### 1. 菜单创建检查
- [ ] menu_id=36 的AI测试菜单已创建
- [ ] 菜单名称为 'AITest'
- [ ] 路径为 '/seigneur/AITest'
- [ ] 组件为 'AITest'

### 2. 旧菜单清理检查
- [ ] menu_id=37 (AITestManagement) 已删除
- [ ] menu_id=38 (APITest) 已删除
- [ ] menu_id=39 (MarkdownTest) 已删除

### 3. 权限分配检查
- [ ] 管理员角色(role_id=1)已分配权限
- [ ] 注册用户角色(role_id=2)已分配权限

### 4. 系统集成检查
- [ ] 前端应用已重启
- [ ] 路由缓存已刷新
- [ ] 页面可正常访问

## 🔧 数据库配置

### 本地开发环境
```yaml
datasource:
  username: root
  password: 123456
  url: **********************************
```

### 测试环境
```yaml
datasource:
  username: fsuser
  password: TM@fs.456
  url: ********************************************************
```

## 🎯 新菜单配置详情

| 字段 | 值 | 说明 |
|------|-----|------|
| menu_id | 36 | 菜单唯一标识 |
| parent_id | 0 | 顶级菜单 |
| menu_name | AITest | 对应组件名称 |
| path | /seigneur/AITest | 路由路径 |
| component | AITest | Vue组件名称 |
| title | AI测试管理 | 显示标题 |
| icon | el-icon-cpu | 菜单图标 |
| order_mun | 30 | 排序（在其他工具前） |

## 🔄 回滚操作

如需回滚到修正前状态：

```sql
-- 删除新菜单
DELETE FROM t_role_menu WHERE MENU_ID = 36;
DELETE FROM t_menu WHERE menu_id = 36;

-- 恢复旧菜单（需要重新执行原始的create_aitest_menu.sql）
```

## 📞 故障排除

### 问题1：MySQL连接失败
**解决方案**:
- 检查MySQL服务是否运行
- 确认用户名密码是否正确
- 验证数据库名称是否存在

### 问题2：权限不足
**解决方案**:
- 确认数据库用户有足够权限
- 检查表是否存在
- 验证角色表数据是否正确

### 问题3：菜单ID冲突
**解决方案**:
- 查询当前最大menu_id
- 调整SQL中的menu_id值
- 重新执行修正脚本

## 📝 注意事项

1. **备份数据**: 执行前建议备份t_menu和t_role_menu表
2. **环境确认**: 先在测试环境验证，再在生产环境执行
3. **应用重启**: 执行后需要重启前端应用以刷新路由缓存
4. **功能验证**: 修正后需要完整测试所有AI测试功能

## 🎉 完成后的效果

修正完成后，用户将看到：
- 菜单栏中只有一个"AI测试管理"入口
- 点击后进入统一的任务管理页面
- 页面包含查询区域、任务列表和分页功能
- 点击"创建任务"打开模态框，包含所有测试功能
- 支持快捷键操作（Ctrl+R刷新，Ctrl+N创建）
