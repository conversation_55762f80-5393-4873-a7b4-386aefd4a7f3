<template>
  <div class="data-comparison-list">
    <!-- 查询区域 -->
    <CardList>
      <CardListItem>
        <el-form :model="queryForm" inline>
          <el-form-item label="状态">
            <el-select v-model="queryForm.overallStatus" placeholder="请选择状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="成功" value="SUCCESS" />
              <el-option label="失败" value="FAILED" />
              <el-option label="部分成功" value="PARTIAL_SUCCESS" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="queryForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleStartTest">开始AI测试</el-button>
          </el-form-item>
        </el-form>
      </CardListItem>
    </CardList>

    <!-- 列表区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="fileMd5" label="文件MD5" width="200" show-overflow-tooltip />
        <el-table-column prop="taskId" label="任务阶段" width="150">
          <template #default="{ row }">
            <el-tag :type="getStageTagType(row.overallStatus)">
              {{ getStageText(row.overallStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="overallStatus" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.overallStatus)">
              {{ getStatusText(row.overallStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="overallScore" label="评分" width="100">
          <template #default="{ row }">
            <span v-if="row.overallScore !== null">{{ row.overallScore }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情模态框 -->
    <DataComparisonDetail
      v-model:visible="detailVisible"
      :record-id="currentRecordId"
      @refresh="handleQuery"
    />

    <!-- 开始测试模态框 -->
    <StartTestModal
      v-model:visible="startTestVisible"
      @success="handleTestStarted"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CardList from '@/components/CardList/CardList.vue'
import CardListItem from '@/components/CardList/CardListItem.vue'
import DataComparisonDetail from './DataComparisonDetail.vue'
import StartTestModal from './StartTestModal.vue'
import { getAITestList, deleteAITest } from '@/api/ai-test'
import type { AITestListRequest } from '@/api/ai-test/types'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const detailVisible = ref(false)
const startTestVisible = ref(false)
const currentRecordId = ref<number | null>(null)
const selectedRows = ref([])

// 查询表单
const queryForm = reactive({
  overallStatus: '',
  dateRange: null as [string, string] | null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 查询数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: AITestListRequest = {
      current: pagination.current,
      size: pagination.size,
      overallStatus: queryForm.overallStatus || undefined,
      startTime: queryForm.dateRange?.[0],
      endTime: queryForm.dateRange?.[1]
    }

    const response = await getAITestList(params)
    if (response.success) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询数据失败:', error)
    ElMessage.error('查询数据失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleQuery = () => {
  pagination.current = 1
  fetchData()
}

const handleReset = () => {
  queryForm.overallStatus = ''
  queryForm.dateRange = null
  pagination.current = 1
  fetchData()
}

const handleStartTest = () => {
  startTestVisible.value = true
}

const handleView = (row: any) => {
  currentRecordId.value = row.id
  detailVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 ${row.taskId} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteAITest(row.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchData()
}

const handleTestStarted = () => {
  ElMessage.success('AI测试任务已启动')
  fetchData()
}

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    SUCCESS: 'success',
    FAILED: 'danger',
    PARTIAL_SUCCESS: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    SUCCESS: '成功',
    FAILED: '失败',
    PARTIAL_SUCCESS: '部分成功'
  }
  return textMap[status] || '未知'
}

const getStageTagType = (status: string) => {
  return status === 'SUCCESS' ? 'success' : 'warning'
}

const getStageText = (status: string) => {
  return status === 'SUCCESS' ? '已完成' : '进行中'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.data-comparison-list {
  padding: 20px;
}

.table-container {
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
