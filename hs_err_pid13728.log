#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 266338304 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=13728, tid=35364
#
# JRE version:  (21.0.7+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitee.com': 

Host: 11th Gen Intel(R) Core(TM) i7-11800H @ 2.30GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Sun Jul 27 01:17:59 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 0.033200 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002d3bd9c9690):  JavaThread "Unknown thread" [_thread_in_vm, id=35364, stack(0x000000e232a00000,0x000000e232b00000) (1024K)]

Stack: [0x000000e232a00000,0x000000e232b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0x6e28c5]
V  [jvm.dll+0x6d634a]
V  [jvm.dll+0x36388b]
V  [jvm.dll+0x36b456]
V  [jvm.dll+0x3bd7e6]
V  [jvm.dll+0x3bdab8]
V  [jvm.dll+0x335fdc]
V  [jvm.dll+0x336ccb]
V  [jvm.dll+0x88b7a9]
V  [jvm.dll+0x3ca9b8]
V  [jvm.dll+0x8747f8]
V  [jvm.dll+0x45f3ce]
V  [jvm.dll+0x4610b1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffa5e86b148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000002d3bda2d790 WorkerThread "GC Thread#0"                     [id=32124, stack(0x000000e232b00000,0x000000e232c00000) (1024K)]
  0x000002d3bda3e5f0 ConcurrentGCThread "G1 Main Marker"            [id=47160, stack(0x000000e232c00000,0x000000e232d00000) (1024K)]
  0x000002d3bda3f0f0 WorkerThread "G1 Conc#0"                       [id=28332, stack(0x000000e232d00000,0x000000e232e00000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa5df590b7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffa5e8dfab8] Heap_lock - owner thread: 0x000002d3bd9c9690

Heap address: 0x0000000702400000, size: 4060 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000002d3d25a0000,0x000002d3d2d90000] _byte_map_base: 0x000002d3ced8e000

Marking Bits: (CMBitMap*) 0x000002d3bda2dda0
 Bits: [0x000002d3d2d90000, 0x000002d3d6d00000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.023 Loaded shared library D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6b59f0000 - 0x00007ff6b59fa000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007ffb5a990000 - 0x00007ffb5aba7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb59690000 - 0x00007ffb59754000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb58120000 - 0x00007ffb584f2000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb58500000 - 0x00007ffb58611000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb097d0000 - 0x00007ffb097e8000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007ffb51350000 - 0x00007ffb5136b000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007ffb59cb0000 - 0x00007ffb59e61000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb57f80000 - 0x00007ffb57fa6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb5a920000 - 0x00007ffb5a949000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb57b20000 - 0x00007ffb57c43000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb57a80000 - 0x00007ffb57b1a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb45d80000 - 0x00007ffb4601b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772\COMCTL32.dll
0x00007ffb59e80000 - 0x00007ffb59f27000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb599f0000 - 0x00007ffb59a21000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000055ef0000 - 0x0000000055efd000 	D:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffb58620000 - 0x00007ffb586d1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb59940000 - 0x00007ffb599e8000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb57f50000 - 0x00007ffb57f78000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb5a530000 - 0x00007ffb5a644000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb4fd20000 - 0x00007ffb4fe25000 	D:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffb586e0000 - 0x00007ffb58f82000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb57d10000 - 0x00007ffb57e4f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb59270000 - 0x00007ffb59603000 	C:\WINDOWS\System32\combase.dll
0x00007ffb58fe0000 - 0x00007ffb59049000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffb574d0000 - 0x00007ffb574da000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb51340000 - 0x00007ffb5134c000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007ffa6fde0000 - 0x00007ffa6fe6d000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007ffa5dc10000 - 0x00007ffa5e9d1000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007ffb59f30000 - 0x00007ffb59fa1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb56810000 - 0x00007ffb5685d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb51710000 - 0x00007ffb51744000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb567f0000 - 0x00007ffb56803000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb56ab0000 - 0x00007ffb56ac8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb1f5e0000 - 0x00007ffb1f5ea000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007ffb54ec0000 - 0x00007ffb550f3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb59fb0000 - 0x00007ffb5a087000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb4bf60000 - 0x00007ffb4bf92000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb57ed0000 - 0x00007ffb57f4b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb097f0000 - 0x00007ffb09810000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772;D:\Program Files (x86)\360\360Safe\safemon;D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitee.com': 
java_class_path (initial): D:/Program Files (x86)/jetbrains/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files (x86)/jetbrains/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4257218560                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4257218560                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Java\jdk-17
CLASSPATH=.;D:\Program Files\Java\jdk-17\lib\dt.jar;D:\Program Files\Java\jdk-17\lib\tools.jar
PATH=D:/Program Files/Git/mingw64/libexec/git-core;D:/Program Files/Git/mingw64/libexec/git-core;D:\Program Files\Git\mingw64\bin;D:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\libnvvp;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\cudnn\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\extras\CUPTI\lib64;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2022.1.1\;D:\LenovoSoftstore\Bandizip\;D:\engine\Java\jdk1.8.0_291\bin;D:\engine\Maven\apache-maven-3.8.6\bin;D:\engine\MinGW\mingw64\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;D:\engine\NetSarang\Xshell 7\;D:\LenovoSoftstore\Tesseract-OCR;D:\Program Files\nvm;D:\Program Files\nodejs;D:\Program Files\Java\jdk-17\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Program Files\apache-maven-3.9.9\bin;d:\Program Files\cursor\resources\app\bin;D:\Program Files\Git\cmd;D:\Program Files\nacos;D:\Program Files\MySQL\MySQL Server 8.4\bin;D:\Program Files\Redis\;D:\work\mcp\cunzhi;D:\Program Files\Python313\Scripts\;D:\Program Files\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\Program Files\cursor\resources\app\bin;D:\Program Files\Microsoft VS Code\bin
USERNAME=xuewen.wang
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13908K (0% of 16624952K total physical memory with 283168K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 5 days 15:54 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x50, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 16 processors :
  Max Mhz: 2304, Current Mhz: 2304, Mhz Limit: 2304

Memory: 4k page, system-wide physical 16235M (276M free)
TotalPageFile size 51523M (AvailPageFile size 161M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 71M, peak: 325M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
