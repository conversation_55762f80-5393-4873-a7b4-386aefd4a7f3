<template>
  <div class="ai-test-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>AI测试管理</h1>
      <p>管理和监控AI数据对比测试任务</p>
    </div>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" type="card" class="demo-tabs">
      <el-tab-pane label="任务管理" name="management">
        <!-- 数据对比列表组件 -->
        <DataComparisonList />
      </el-tab-pane>

      <el-tab-pane label="API测试" name="api">
        <APITest />
      </el-tab-pane>

      <el-tab-pane label="Markdown测试" name="markdown">
        <MarkdownTest />
      </el-tab-pane>

      <el-tab-pane label="性能测试" name="performance">
        <MarkdownPerformanceTest />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DataComparisonList from './components/DataComparisonList.vue'
import APITest from '@/components/AITest/APITest.vue'
import MarkdownTest from '@/components/AITest/MarkdownTest.vue'
import MarkdownPerformanceTest from '@/components/AITest/MarkdownPerformanceTest.vue'

// 响应式数据
const activeTab = ref('management')
</script>

<style scoped>
.ai-test-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.demo-tabs {
  margin-top: 20px;
}
</style>
