# [005] AI测试功能全面重构计划

**创建时间**: 2025-08-04T15:45:34+08:00
**更新时间**: 2025-08-04T16:02:45+08:00
**项目**: beacon-tower AI测试模块
**方案**: 全面重构方案
**状态**: 执行中 - 阶段三完成

## 📋 重构目标

基于RIPER-5工作流，对前端和后端的AI测试功能进行全面重构，实现：
- 完整的数据库结构优化
- 标准化的API接口设计
- 现代化的前端组件架构
- 高性能的数据对比功能
- 完善的用户体验设计

## 🏗️ 重构架构设计

### 数据库层重构
- 完整的表结构重新设计
- 优化索引和约束策略
- 增强数据完整性保证
- 支持高并发访问

### 后端服务重构
- RESTful API标准化设计
- 异步任务处理优化
- SSE实时通信增强
- 完善的异常处理机制

### 前端组件重构
- Vue 3 + TypeScript现代化架构
- 组件化设计模式
- 高性能数据对比视图
- 响应式用户界面

## 📝 详细任务计划

### 阶段一：数据库结构重构 (2天)

#### 任务1.1: 数据库表结构重新设计 ✅
**预估时间**: 1天
**优先级**: 高
**依赖**: 无
**状态**: 已完成

**实施内容**:
1. 重新设计`t_data_comparison`表结构
   - 添加`file_md5`字段 (VARCHAR(32), 索引)
   - 优化时间字段配置 (自动更新)
   - 增加状态字段枚举约束
   - 添加业务字段索引

2. 重新设计`t_data_comparison_stage`表结构
   - 添加`file_md5`字段 (VARCHAR(32), 索引)
   - 优化数据存储字段 (LONGTEXT -> JSON)
   - 增强外键约束
   - 添加复合索引优化查询

3. 创建数据迁移脚本
   - 保证数据完整性
   - 支持回滚操作
   - 验证数据一致性

**验收标准**:
- 表结构符合设计要求
- 索引策略优化查询性能
- 数据迁移无丢失
- 约束规则正确生效

#### 任务1.2: 实体类和Mapper重构 ✅
**预估时间**: 1天
**优先级**: 高
**依赖**: 任务1.1
**状态**: 已完成

**实施内容**:
1. 重构`TDataComparison`实体类
   - 添加`fileMd5`字段映射
   - 优化字段注解配置
   - 增强验证规则
   - 完善JavaDoc文档

2. 重构`TDataComparisonStage`实体类
   - 添加`fileMd5`字段映射
   - 优化JSON字段处理
   - 增强类型安全
   - 完善字段验证

3. 重构Mapper接口
   - 优化查询方法设计
   - 增加复杂查询支持
   - 完善分页查询
   - 添加统计查询方法

**验收标准**:
- 实体类映射正确
- Mapper方法功能完整
- 类型安全保证
- 查询性能优化

### 阶段二：后端API重构 (3天)

#### 任务2.1: API接口标准化重构 ✅
**预估时间**: 1.5天
**优先级**: 高
**依赖**: 阶段一完成
**状态**: 已完成

**实施内容**:
1. 重构数据对比列表接口
   - 路径: `GET /api/beacon-tower/ai-test/data-comparison/list`
   - 增强查询参数支持
   - 优化分页响应格式
   - 添加字段筛选功能

2. 重构详情查询接口
   - 路径: `GET /api/beacon-tower/ai-test/data-comparison/detail/{id}`
   - 完整数据结构返回
   - 包含差异对比数据
   - 支持数据格式转换

3. 新增删除接口
   - 路径: `DELETE /api/beacon-tower/ai-test/data-comparison/{id}`
   - 支持软删除/硬删除
   - 权限验证机制
   - 操作日志记录

**验收标准**:
- API接口符合RESTful规范
- 响应格式标准化
- 错误处理完善
- 性能指标达标

#### 任务2.2: 业务流程重构 ✅
**预估时间**: 1.5天
**优先级**: 高
**依赖**: 任务2.1
**状态**: 已完成

**实施内容**:
1. 重构数据对比启动流程
   - 立即计算并保存file_md5
   - 优化数据持久化逻辑
   - 增强事务管理
   - 完善异常回滚

2. 重构异步AI处理流程
   - 优化消息队列机制
   - 增强任务状态管理
   - 完善进度推送逻辑
   - 添加失败重试机制

3. 重构SSE推送机制
   - 优化连接管理
   - 增强消息格式
   - 完善错误处理
   - 添加连接监控

**验收标准**:
- 业务流程稳定可靠
- 异步处理高效
- SSE推送实时准确
- 异常处理完善

### 阶段三：前端组件重构 (4天)

#### 任务3.1: 列表页面全面重构 ✅
**预估时间**: 2天
**优先级**: 高
**依赖**: 阶段二完成
**状态**: 已完成

**实施内容**:
1. 重构列表组件架构
   - 采用Vue 3 Composition API
   - 优化组件拆分策略
   - 增强状态管理
   - 完善类型定义

2. 重构表头字段设计
   - 文件MD5字段显示
   - 任务阶段状态展示
   - 状态标签样式优化
   - 评分数值格式化
   - 时间格式标准化

3. 重构操作列功能
   - 查看按钮样式优化
   - 删除确认对话框
   - 权限控制逻辑
   - 操作反馈机制

**验收标准**:
- 列表展示完整准确
- 操作功能正常
- 用户体验良好
- 性能表现优秀

#### 任务3.2: 详情模态框重构 ✅
**预估时间**: 2天
**优先级**: 高
**依赖**: 任务3.1
**状态**: 已完成

**实施内容**:
1. 重构模态框架构
   - 响应式布局设计
   - 组件化拆分
   - 状态管理优化
   - 生命周期管理

2. 实现数据对比功能
   - 集成react-diff-view组件
   - 实现左右分栏布局
   - 差异高亮显示
   - 支持大数据量对比

3. 实现AI评估结果展示
   - 独立区域设计
   - Markdown渲染支持
   - 实时更新机制
   - 交互优化

**验收标准**:
- 模态框功能完整
- 数据对比清晰
- AI结果展示准确
- 交互体验流畅

### 阶段四：系统集成优化 (1天)

#### 任务4.1: 路由和权限配置
**预估时间**: 0.5天
**优先级**: 中
**依赖**: 阶段三完成

**实施内容**:
1. 更新API路由配置
2. 配置网关转发规则
3. 完善权限控制
4. 测试路由功能

#### 任务4.2: 最终集成测试
**预估时间**: 0.5天
**优先级**: 高
**依赖**: 任务4.1

**实施内容**:
1. 端到端功能测试
2. 性能压力测试
3. 用户体验验证
4. 问题修复优化

## 🎯 总体时间安排

- **总预估时间**: 10天
- **关键路径**: 数据库重构 → 后端API重构 → 前端组件重构 → 系统集成
- **风险缓冲**: 2天
- **实际交付**: 12天

## ✅ 验收标准

1. 所有新增字段正确显示和存储
2. 数据对比功能清晰准确
3. 删除功能安全可靠
4. SSE连接稳定实时
5. 时间字段自动管理
6. API接口标准规范
7. 用户体验流畅友好
8. 系统性能稳定高效
