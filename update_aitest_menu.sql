-- ==========================================
-- AI测试模块菜单修正SQL
-- 修正时间: 2025-07-29
-- 说明: 根据重构后的页面架构，简化菜单结构为单一入口
-- ==========================================

-- 首先删除旧的菜单配置（如果存在）
DELETE FROM t_role_menu WHERE MENU_ID IN (36, 37, 38, 39);
DELETE FROM t_menu WHERE menu_id IN (36, 37, 38, 39);

-- ==========================================
-- 创建新的简化菜单结构
-- ==========================================

-- 1. 创建AI测试菜单 (单一入口，无子菜单)
INSERT INTO t_menu (
    menu_id,
    parent_id,
    menu_name,
    path,
    component,
    redirect,
    perms,
    title,
    icon,
    active_menu,
    always_show,
    hidden,
    type,
    order_mun,
    create_time,
    modify_time,
    status
) VALUES (
    36,                                          -- menu_id: 36
    0,                                           -- parent_id: 0 (顶级菜单)
    'AITest',                                    -- menu_name: AITest (对应AITest.vue组件)
    '/seigneur/AITest',                          -- path: 路由路径
    'AITest',                                    -- component: AITest组件 (直接指向AITest.vue)
    NULL,                                        -- redirect: 无重定向
    NULL,                                        -- perms: 权限标识(可为空)
    'AI测试管理',                                -- title: 菜单标题
    'el-icon-cpu',                               -- icon: CPU图标，符合AI测试主题
    NULL,                                        -- active_menu: 高亮菜单路径(可为空)
    NULL,                                        -- always_show: NULL-默认显示规则
    NULL,                                        -- hidden: NULL-不隐藏
    0,                                           -- type: 0-菜单类型
    30,                                          -- order_mun: 30 (在其他工具99之前)
    NOW(),                                       -- create_time: 当前时间
    NOW(),                                       -- modify_time: 当前时间
    0                                            -- status: 0-正常状态
);

-- ==========================================
-- 权限分配SQL (为现有角色分配新菜单权限)
-- ==========================================

-- 2. 为管理员角色(ROLE_ADMIN, role_id=1)分配AI测试菜单权限
INSERT INTO t_role_menu (ROLE_ID, MENU_ID) VALUES (1, 36);

-- 3. 为注册用户角色(ROLE_REGISTER, role_id=2)分配AI测试菜单权限
INSERT INTO t_role_menu (ROLE_ID, MENU_ID) VALUES (2, 36);

-- ==========================================
-- 验证SQL (可选执行，用于验证修正结果)
-- ==========================================

-- 查看新创建的菜单
SELECT 
    menu_id, 
    parent_id, 
    menu_name, 
    path, 
    component,
    title, 
    icon, 
    order_mun, 
    status 
FROM t_menu 
WHERE menu_id = 36;

-- 查看角色权限分配
SELECT 
    rm.ROLE_ID, 
    r.ROLE_NAME, 
    rm.MENU_ID, 
    m.menu_name, 
    m.title 
FROM t_role_menu rm 
JOIN t_role r ON rm.ROLE_ID = r.ROLE_ID 
JOIN t_menu m ON rm.MENU_ID = m.menu_id 
WHERE rm.MENU_ID = 36 
ORDER BY rm.ROLE_ID;

-- 查看完整菜单层级结构（包含AI测试菜单）
SELECT 
    CASE WHEN parent_id = 0 THEN CONCAT('├─ ', title) 
         ELSE CONCAT('│  └─ ', title) END as menu_structure,
    menu_id, 
    path, 
    component,
    order_mun
FROM t_menu 
WHERE status = 0 
ORDER BY 
    CASE WHEN parent_id = 0 THEN menu_id ELSE parent_id END,
    CASE WHEN parent_id = 0 THEN 0 ELSE 1 END,
    order_mun;

-- ==========================================
-- 架构说明
-- ==========================================
-- 
-- 重构后的架构特点：
-- 1. 单一菜单入口：只有一个"AI测试管理"菜单项
-- 2. 统一页面管理：所有功能集成在AITest.vue页面中
-- 3. 模态框交互：API测试、Markdown测试、性能测试都在模态框中
-- 4. 简化导航：用户通过单一入口访问所有AI测试功能
-- 
-- 页面功能：
-- - 查询区域：支持任务名称、状态、创建时间筛选
-- - 列表区域：表格展示任务列表，支持分页和批量操作
-- - 创建任务：点击"创建任务"按钮打开模态框
-- - 模态框内容：包含API测试、Markdown测试、性能测试三个Tab页
-- 
-- 快捷键：
-- - Ctrl+R: 刷新任务列表
-- - Ctrl+N: 创建新任务
-- 
-- ==========================================
-- 执行说明
-- ==========================================
-- 1. 执行前请确认当前最大menu_id，如有冲突请调整
-- 2. 建议先在测试环境执行验证
-- 3. 执行后需要重启前端应用以刷新路由缓存
-- 4. 如需回滚，请执行以下语句:
--    DELETE FROM t_role_menu WHERE MENU_ID = 36;
--    DELETE FROM t_menu WHERE menu_id = 36;
-- ==========================================
