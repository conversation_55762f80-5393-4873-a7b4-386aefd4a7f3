<template>
  <el-dialog
    v-model="dialogVisible"
    title="开始AI测试"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      @submit.prevent
    >
      <el-form-item label="测试ID列表" prop="ids" required>
        <el-input
          v-model="formData.idsText"
          type="textarea"
          :rows="4"
          placeholder="请输入测试ID，每行一个或用逗号分隔"
        />
        <div class="form-tip">
          支持多种格式：每行一个ID，或用逗号、分号、空格分隔
        </div>
      </el-form-item>

      <el-form-item label="UAT环境URL">
        <el-input
          v-model="formData.uatBaseUrl"
          placeholder="留空使用默认配置"
        />
      </el-form-item>

      <el-form-item label="TEST环境URL">
        <el-input
          v-model="formData.testBaseUrl"
          placeholder="留空使用默认配置"
        />
      </el-form-item>

      <el-form-item label="测试阶段">
        <el-checkbox-group v-model="formData.stages">
          <el-checkbox label="recognize">识别阶段</el-checkbox>
          <el-checkbox label="extraction">提取阶段</el-checkbox>
          <el-checkbox label="structured">结构化阶段</el-checkbox>
          <el-checkbox label="transformer">转换阶段</el-checkbox>
        </el-checkbox-group>
        <div class="form-tip">
          不选择则默认执行所有阶段
        </div>
      </el-form-item>

      <el-form-item label="高级配置">
        <div class="advanced-config">
          <el-checkbox v-model="formData.enableAiEvaluation">
            启用AI评估
          </el-checkbox>
          <el-checkbox v-model="formData.disableChunking">
            禁用数据分片
          </el-checkbox>
        </div>
      </el-form-item>

      <el-form-item label="并发限制">
        <el-input-number
          v-model="formData.concurrentLimit"
          :min="1"
          :max="10"
          placeholder="默认为3"
        />
        <div class="form-tip">
          同时处理的最大任务数量
        </div>
      </el-form-item>

      <el-form-item label="超时时间(秒)">
        <el-input-number
          v-model="formData.timeoutSeconds"
          :min="10"
          :max="300"
          placeholder="默认为60"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          开始测试
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { startAITest } from '@/api/ai-test'
import type { AITestStartRequest } from '@/api/ai-test/types'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: [taskId: string]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

const formData = reactive({
  idsText: '',
  uatBaseUrl: '',
  testBaseUrl: '',
  stages: [] as string[],
  enableAiEvaluation: true,
  disableChunking: false,
  concurrentLimit: 3,
  timeoutSeconds: 60
})

// 表单验证规则
const formRules: FormRules = {
  idsText: [
    { required: true, message: '请输入测试ID', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        const ids = parseIds(value)
        if (ids.length === 0) {
          callback(new Error('请输入有效的测试ID'))
        } else if (ids.length > 100) {
          callback(new Error('测试ID数量不能超过100个'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 解析ID列表
const parseIds = (text: string): string[] => {
  if (!text) return []
  
  // 支持多种分隔符：换行、逗号、分号、空格
  const ids = text
    .split(/[\n,;，；\s]+/)
    .map(id => id.trim())
    .filter(id => id.length > 0)
  
  return [...new Set(ids)] // 去重
}

// 事件处理
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 构建请求参数
    const ids = parseIds(formData.idsText)
    const params: AITestStartRequest = {
      ids,
      enableAiEvaluation: formData.enableAiEvaluation,
      disableChunking: formData.disableChunking,
      concurrentLimit: formData.concurrentLimit,
      timeoutSeconds: formData.timeoutSeconds
    }

    // 可选参数
    if (formData.uatBaseUrl) {
      params.uatBaseUrl = formData.uatBaseUrl
    }
    if (formData.testBaseUrl) {
      params.testBaseUrl = formData.testBaseUrl
    }
    if (formData.stages.length > 0) {
      params.stages = formData.stages
    }

    const response = await startAITest(params)
    if (response.success) {
      ElMessage.success('AI测试任务启动成功')
      emit('success', response.data.taskId)
      handleClose()
    } else {
      ElMessage.error(response.message || '启动失败')
    }
  } catch (error) {
    console.error('启动AI测试失败:', error)
    ElMessage.error('启动AI测试失败')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.assign(formData, {
    idsText: '',
    uatBaseUrl: '',
    testBaseUrl: '',
    stages: [],
    enableAiEvaluation: true,
    disableChunking: false,
    concurrentLimit: 3,
    timeoutSeconds: 60
  })
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.advanced-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
