#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=46052, tid=24736
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.7******.130-jcef (21.0.7+9) (build 21.0.7+9-b895.130)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.7******.130-jcef (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitee.com': 

Host: 11th Gen Intel(R) Core(TM) i7-11800H @ 2.30GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Sun Jul 27 00:57:59 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 0.276206 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001f97cc0fa80):  JavaThread "main"             [_thread_in_vm, id=24736, stack(0x000000f5a5d00000,0x000000f5a5e00000) (1024K)]

Stack: [0x000000f5a5d00000,0x000000f5a5e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0x8bf18e]
V  [jvm.dll+0x686d15]
V  [jvm.dll+0x686d7a]
V  [jvm.dll+0x689596]
V  [jvm.dll+0x689462]
V  [jvm.dll+0x6876ce]
V  [jvm.dll+0x273c40]
V  [jvm.dll+0x21dd3b]
V  [jvm.dll+0x213229]
V  [jvm.dll+0x5c47b3]
V  [jvm.dll+0x224c6b]
V  [jvm.dll+0x83b08c]
V  [jvm.dll+0x83c132]
V  [jvm.dll+0x83c6f4]
V  [jvm.dll+0x83c388]
V  [jvm.dll+0x27629b]
V  [jvm.dll+0x2764b5]
V  [jvm.dll+0x5e76b7]
V  [jvm.dll+0x5ea7ae]
V  [jvm.dll+0x3e7900]
V  [jvm.dll+0x3e6f7d]
C  0x000001f90b3ea0a0

The last pc belongs to invokestatic (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.security.MessageDigest.getInstance(Ljava/lang/String;)Ljava/security/MessageDigest;+61 java.base@21.0.7
j  sun.security.ec.ECDSASignature.<init>(Ljava/lang/String;Z)V+6 jdk.crypto.ec@21.0.7
j  sun.security.ec.ECDSASignature.<init>(Ljava/lang/String;)V+3 jdk.crypto.ec@21.0.7
j  sun.security.ec.ECDSASignature$SHA1.<init>()V+3 jdk.crypto.ec@21.0.7
j  sun.security.ec.SunEC$ProviderService.newInstance(Ljava/lang/Object;)Ljava/lang/Object;+142 jdk.crypto.ec@21.0.7
j  java.security.Signature.isSpi(Ljava/security/Provider$Service;)Z+38 java.base@21.0.7
j  java.security.Signature.getInstance(Ljava/lang/String;)Ljava/security/Signature;+89 java.base@21.0.7
j  sun.security.ssl.JsseJce$EcAvailability.<clinit>()V+4 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000001f90b3d10e7
j  sun.security.ssl.JsseJce.isEcAvailable()Z+0 java.base@21.0.7
j  sun.security.ssl.CipherSuite$KeyExchange.isAvailable()Z+29 java.base@21.0.7
j  sun.security.ssl.CipherSuite.isAvailable()Z+23 java.base@21.0.7
j  sun.security.ssl.SSLContextImpl.getApplicableCipherSuites(Ljava/util/Collection;Ljava/util/List;)Ljava/util/List;+50 java.base@21.0.7
j  sun.security.ssl.SSLContextImpl.getApplicableSupportedCipherSuites(Ljava/util/List;)Ljava/util/List;+4 java.base@21.0.7
j  sun.security.ssl.SSLContextImpl$AbstractTLSContext.<clinit>()V+84 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000001f90b3d10e7
j  java.lang.Class.forName0(Ljava/lang/String;ZLjava/lang/ClassLoader;Ljava/lang/Class;)Ljava/lang/Class;+0 java.base@21.0.7
j  java.lang.Class.forName(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Class;+19 java.base@21.0.7
j  java.lang.Class.forName(Ljava/lang/String;)Ljava/lang/Class;+6 java.base@21.0.7
j  java.security.Provider$Service.getImplClass()Ljava/lang/Class;+64 java.base@21.0.7
j  java.security.Provider$Service.getDefaultConstructor()Ljava/lang/reflect/Constructor;+46 java.base@21.0.7
j  java.security.Provider$Service.newInstanceOf()Ljava/lang/Object;+1 java.base@21.0.7
j  java.security.Provider$Service.newInstanceUtil(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;+5 java.base@21.0.7
j  java.security.Provider$Service.newInstance(Ljava/lang/Object;)Ljava/lang/Object;+216 java.base@21.0.7
j  sun.security.jca.GetInstance.getInstance(Ljava/security/Provider$Service;Ljava/lang/Class;)Lsun/security/jca/GetInstance$Instance;+2 java.base@21.0.7
j  sun.security.jca.GetInstance.getInstance(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/String;)Lsun/security/jca/GetInstance$Instance;+56 java.base@21.0.7
j  javax.net.ssl.SSLContext.getInstance(Ljava/lang/String;)Ljavax/net/ssl/SSLContext;+12 java.base@21.0.7
j  externalApp.ExternalAppUtil.sendIdeRequest(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)LexternalApp/ExternalAppUtil$Result;+34
j  git4idea.http.GitAskPassApp.main([Ljava/lang/String;)V+37
v  ~StubRoutines::call_stub 0x000001f90b3d10e7
invokestatic  184 invokestatic  [0x000001f90b3ea000, 0x000001f90b3ea2c8]  712 bytes
[MachCode]
  0x000001f90b3ea000: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000001f90b3ea020: 4424 0800 | 0000 00eb | 0150 4c89 | 6dc0 410f | b755 0148 | 8b4d d0c1 | e202 8b5c | d138 c1eb 
  0x000001f90b3ea040: 1081 e3ff | 0000 0081 | fbb8 0000 | 000f 84b4 | 0000 00bb | b800 0000 | e805 0000 | 00e9 9900 
  0x000001f90b3ea060: 0000 488b | d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 
  0x000001f90b3ea080: 0000 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 106f | ff5d fa7f | 0000 ffd0 
  0x000001f90b3ea0a0: 4883 c408 | e90c 0000 | 0048 b810 | 6fff 5dfa | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 
  0x000001f90b3ea0c0: 0000 0000 | 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f 
  0x000001f90b3ea0e0: 0800 0f84 | 0500 0000 | e913 6efe | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c341 | 0fb7 5501 
  0x000001f90b3ea100: 488b 4dd0 | c1e2 0248 | 8b5c d140 | 488b 5b08 | 488b 5b08 | 488b 5b18 | 80bb 4101 | 0000 040f 
  0x000001f90b3ea120: 840d 0000 | 004c 3bbb | 4801 0000 | 0f85 21ff | ffff 488b | 5cd1 408b | 54d1 50c1 | ea1c 49ba 
  0x000001f90b3ea140: 50f7 8e5e | fa7f 0000 | 498b 14d2 | 5248 8b45 | d848 85c0 | 0f84 1200 | 0000 4883 | 4008 0148 
  0x000001f90b3ea160: 8358 0800 | 4883 c010 | 4889 45d8 | 488b 45d8 | 4885 c00f | 843d 0100 | 0080 78f0 | 0a0f 8533 
  0x000001f90b3ea180: 0100 0048 | 83c0 084c | 8b68 f841 | 83ed 0041 | 83fd 020f | 8c12 0100 | 004c 8b6b | 0845 0fb7 
  0x000001f90b3ea1a0: 6d2e 4c2b | 2841 83ed | 014e 8b6c | ec08 4d85 | ed75 0ef6 | 4008 0175 | 58f0 4883 | 4808 01eb 
  0x000001f90b3ea1c0: 5045 8b6d | 0849 ba00 | 0000 1bf9 | 0100 004d | 03ea 4d8b | d54c 3368 | 0849 f7c5 | fcff ffff 
  0x000001f90b3ea1e0: 742f 41f6 | c502 7529 | 4883 7808 | 0074 1e48 | 8378 0801 | 7417 4d8b | ea4c 3368 | 0849 f7c5 
  0x000001f90b3ea200: fcff ffff | 740b 4883 | 4808 02eb | 044c 8968 | 0848 83c0 | 104c 8b68 | e841 83ed | 0241 83fd 
  0x000001f90b3ea220: 020f 8c84 | 0000 004c | 8b6b 0845 | 0fb7 6d2e | 4c2b 2841 | 83ed 014e | 8b6c ec08 | 4d85 ed75 
  0x000001f90b3ea240: 0ef6 4008 | 0175 58f0 | 4883 4808 | 01eb 5045 | 8b6d 0849 | ba00 0000 | 1bf9 0100 | 004d 03ea 
  0x000001f90b3ea260: 4d8b d54c | 3368 0849 | f7c5 fcff | ffff 742f | 41f6 c502 | 7529 4883 | 7808 0074 | 1e48 8378 
  0x000001f90b3ea280: 0801 7417 | 4d8b ea4c | 3368 0849 | f7c5 fcff | ffff 740b | 4883 4808 | 02eb 044c | 8968 0848 
  0x000001f90b3ea2a0: 83c0 104c | 8b68 d841 | 83ed 0441 | c1e5 0349 | 03c5 4889 | 45d8 4c8d | 6c24 084c | 896d f0ff 
  0x000001f90b3ea2c0: 6368 660f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001f91abaaa80, length=12, elements={
0x000001f97cc0fa80, 0x000001f91abac440, 0x000001f91abad350, 0x000001f91abb1280,
0x000001f91abb2060, 0x000001f91abb2be0, 0x000001f91abb3ec0, 0x000001f91abc47c0,
0x000001f91abc68d0, 0x000001f91acff410, 0x000001f91ad4d320, 0x000001f91ad4f370
}

Java Threads: ( => current thread )
=>0x000001f97cc0fa80 JavaThread "main"                              [_thread_in_vm, id=24736, stack(0x000000f5a5d00000,0x000000f5a5e00000) (1024K)]
  0x000001f91abac440 JavaThread "Reference Handler"          daemon [_thread_blocked, id=34420, stack(0x000000f5a6500000,0x000000f5a6600000) (1024K)]
  0x000001f91abad350 JavaThread "Finalizer"                  daemon [_thread_blocked, id=43192, stack(0x000000f5a6600000,0x000000f5a6700000) (1024K)]
  0x000001f91abb1280 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=50316, stack(0x000000f5a6700000,0x000000f5a6800000) (1024K)]
  0x000001f91abb2060 JavaThread "Attach Listener"            daemon [_thread_blocked, id=50420, stack(0x000000f5a6800000,0x000000f5a6900000) (1024K)]
  0x000001f91abb2be0 JavaThread "Service Thread"             daemon [_thread_blocked, id=53432, stack(0x000000f5a6900000,0x000000f5a6a00000) (1024K)]
  0x000001f91abb3ec0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=26908, stack(0x000000f5a6a00000,0x000000f5a6b00000) (1024K)]
  0x000001f91abc47c0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=2704, stack(0x000000f5a6b00000,0x000000f5a6c00000) (1024K)]
  0x000001f91abc68d0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=44808, stack(0x000000f5a6c00000,0x000000f5a6d00000) (1024K)]
  0x000001f91acff410 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=49560, stack(0x000000f5a6d00000,0x000000f5a6e00000) (1024K)]
  0x000001f91ad4d320 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8480, stack(0x000000f5a6e00000,0x000000f5a6f00000) (1024K)]
  0x000001f91ad4f370 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=49068, stack(0x000000f5a6f00000,0x000000f5a7000000) (1024K)]
Total: 12

Other Threads:
  0x000001f91ab97b90 VMThread "VM Thread"                           [id=21524, stack(0x000000f5a6400000,0x000000f5a6500000) (1024K)]
  0x000001f91a3f3c50 WatcherThread "VM Periodic Task Thread"        [id=36212, stack(0x000000f5a6300000,0x000000f5a6400000) (1024K)]
  0x000001f97e6ee060 WorkerThread "GC Thread#0"                     [id=29888, stack(0x000000f5a5e00000,0x000000f5a5f00000) (1024K)]
  0x000001f97e6fefb0 ConcurrentGCThread "G1 Main Marker"            [id=42284, stack(0x000000f5a5f00000,0x000000f5a6000000) (1024K)]
  0x000001f97e6ffab0 WorkerThread "G1 Conc#0"                       [id=40984, stack(0x000000f5a6000000,0x000000f5a6100000) (1024K)]
  0x000001f91a2c5450 ConcurrentGCThread "G1 Refine#0"               [id=21860, stack(0x000000f5a6100000,0x000000f5a6200000) (1024K)]
  0x000001f91a2c5fc0 ConcurrentGCThread "G1 Service"                [id=10212, stack(0x000000f5a6200000,0x000000f5a6300000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffa5e8dd838] Metaspace_lock - owner thread: 0x000001f97cc0fa80

Heap address: 0x0000000702400000, size: 4060 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001f91b000000-0x000001f91bd10000-0x000001f91bd10000), size 13697024, SharedBaseAddress: 0x000001f91b000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001f91c000000-0x000001f95c000000, reserved size: 1073741824
Narrow klass base: 0x000001f91b000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 16 total, 16 available
 Memory: 16235M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4060M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 260096K, used 4096K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 0 survivors (0K)
 Metaspace       used 2271K, committed 2368K, reserved 1114112K
  class space    used 236K, committed 320K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000702400000, 0x0000000702400000, 0x0000000702600000|  0%| F|  |TAMS 0x0000000702400000| PB 0x0000000702400000| Untracked 
|   1|0x0000000702600000, 0x0000000702600000, 0x0000000702800000|  0%| F|  |TAMS 0x0000000702600000| PB 0x0000000702600000| Untracked 
|   2|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|   3|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|   4|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|   5|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|   6|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|   7|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|   8|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|   9|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|  10|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|  11|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  12|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  13|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  14|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  15|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  16|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  17|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  18|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  19|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  20|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  21|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  22|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  23|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  24|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  25|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  26|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  27|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  28|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  29|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  30|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  31|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  32|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  33|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  34|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  35|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  36|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  37|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  38|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  39|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  40|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  41|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  42|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  43|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  44|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  45|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  46|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  47|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  48|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  49|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  50|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  51|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  52|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  53|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  54|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  55|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  56|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  57|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  58|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  59|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  60|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  61|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  62|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  63|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  64|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  65|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  66|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  67|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  68|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  69|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  70|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  71|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  72|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  73|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  74|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  75|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  76|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  77|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  78|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  79|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  80|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  81|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
|  82|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
|  83|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
|  84|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
|  85|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
|  86|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
|  87|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
|  88|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
|  89|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
|  90|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
|  91|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
|  92|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
|  93|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
|  94|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
|  95|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
|  96|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
|  97|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
|  98|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
|  99|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
| 100|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
| 101|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 102|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
| 103|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
| 104|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
| 105|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
| 106|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
| 107|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
| 108|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
| 109|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
| 110|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked 
| 111|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked 
| 112|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
| 113|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
| 114|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
| 115|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
| 116|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
| 117|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
| 118|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
| 119|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
| 120|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
| 121|0x0000000711600000, 0x0000000711600000, 0x0000000711800000|  0%| F|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Untracked 
| 122|0x0000000711800000, 0x0000000711800000, 0x0000000711a00000|  0%| F|  |TAMS 0x0000000711800000| PB 0x0000000711800000| Untracked 
| 123|0x0000000711a00000, 0x0000000711a00000, 0x0000000711c00000|  0%| F|  |TAMS 0x0000000711a00000| PB 0x0000000711a00000| Untracked 
| 124|0x0000000711c00000, 0x0000000711ce14c8, 0x0000000711e00000| 44%| E|  |TAMS 0x0000000711c00000| PB 0x0000000711c00000| Complete 
| 125|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| E|CS|TAMS 0x0000000711e00000| PB 0x0000000711e00000| Complete 
| 126|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| E|CS|TAMS 0x0000000712000000| PB 0x0000000712000000| Complete 

Card table byte_map: [0x000001f913770000,0x000001f913f60000] _byte_map_base: 0x000001f90ff5e000

Marking Bits: (CMBitMap*) 0x000001f97e6ee760
 Bits: [0x000001f913f60000, 0x000001f917ed0000)

Polling page: 0x000001f97cda0000

Metaspace:

Usage:
  Non-class:      1.99 MB used.
      Class:    236.73 KB used.
       Both:      2.22 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       2.00 MB (  3%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     320.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       2.31 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.94 MB
       Class:  15.73 MB
        Both:  27.67 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 10.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 37.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 17.
num_chunk_merges: 0.
num_chunk_splits: 11.
num_chunks_enlarged: 10.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=96Kb max_used=96Kb free=119071Kb
 bounds [0x000001f90bb20000, 0x000001f90bd90000, 0x000001f912f80000]
CodeHeap 'profiled nmethods': size=119104Kb used=515Kb max_used=515Kb free=118588Kb
 bounds [0x000001f903f80000, 0x000001f9041f0000, 0x000001f90b3d0000]
CodeHeap 'non-nmethods': size=7488Kb used=2030Kb max_used=2046Kb free=5457Kb
 bounds [0x000001f90b3d0000, 0x000001f90b640000, 0x000001f90bb20000]
 total_blobs=799 nmethods=351 adapters=352
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.266 Thread 0x000001f91acff410  342       3       java.lang.Integer::equals (29 bytes)
Event: 0.266 Thread 0x000001f91abc47c0  343       4       java.util.Objects::requireNonNull (14 bytes)
Event: 0.266 Thread 0x000001f91acff410 nmethod 342 0x000001f903ffe590 code [0x000001f903ffe740, 0x000001f903ffeb08]
Event: 0.266 Thread 0x000001f91abc47c0 nmethod 343 0x000001f90bb37b10 code [0x000001f90bb37ca0, 0x000001f90bb37d28]
Event: 0.267 Thread 0x000001f91acff410  344       3       java.lang.Character::toLowerCase (9 bytes)
Event: 0.267 Thread 0x000001f91acff410 nmethod 344 0x000001f903ffec10 code [0x000001f903ffedc0, 0x000001f903ffefb8]
Event: 0.267 Thread 0x000001f91acff410  345       3       java.lang.Character::toLowerCase (6 bytes)
Event: 0.267 Thread 0x000001f91abc68d0  346       3       java.lang.StringLatin1::getChar (9 bytes)
Event: 0.267 Thread 0x000001f91abc68d0 nmethod 346 0x000001f903fff090 code [0x000001f903fff220, 0x000001f903fff340]
Event: 0.267 Thread 0x000001f91abc68d0  350       3       java.util.regex.Pattern$SliceI::match (96 bytes)
Event: 0.267 Thread 0x000001f91acff410 nmethod 345 0x000001f903fff410 code [0x000001f903fff5e0, 0x000001f903fff838]
Event: 0.267 Thread 0x000001f91acff410  348       3       java.util.regex.ASCII::toLower (16 bytes)
Event: 0.267 Thread 0x000001f91acff410 nmethod 348 0x000001f903fff910 code [0x000001f903fffac0, 0x000001f903fffcb0]
Event: 0.267 Thread 0x000001f91acff410  349       3       java.util.regex.ASCII::isUpper (18 bytes)
Event: 0.267 Thread 0x000001f91acff410 nmethod 349 0x000001f903fffd90 code [0x000001f903ffff20, 0x000001f904000070]
Event: 0.267 Thread 0x000001f91acff410  347       1       java.util.LinkedHashMap::removeEldestEntry (2 bytes)
Event: 0.267 Thread 0x000001f91acff410 nmethod 347 0x000001f90bb37e10 code [0x000001f90bb37fa0, 0x000001f90bb38068]
Event: 0.267 Thread 0x000001f91abc68d0 nmethod 350 0x000001f904000110 code [0x000001f904000320, 0x000001f904000948]
Event: 0.268 Thread 0x000001f91abc68d0  351       3       jdk.internal.util.Preconditions::checkFromIndexSize (25 bytes)
Event: 0.268 Thread 0x000001f91abc68d0 nmethod 351 0x000001f904000b90 code [0x000001f904000d40, 0x000001f904000ee8]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.023 Loaded shared library D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\java.dll
Event: 0.080 Loaded shared library D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\zip.dll

Deoptimization events (4 events):
Event: 0.230 Thread 0x000001f97cc0fa80 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001f90bb33894 relative=0x0000000000000074
Event: 0.230 Thread 0x000001f97cc0fa80 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001f90bb33894 method=java.lang.CharacterDataLatin1.toLowerCase(I)I @ 16 c2
Event: 0.230 Thread 0x000001f97cc0fa80 DEOPT PACKING pc=0x000001f90bb33894 sp=0x000000f5a5dfafc0
Event: 0.230 Thread 0x000001f97cc0fa80 DEOPT UNPACKING pc=0x000001f90b427da2 sp=0x000000f5a5dfaf50 mode 2

Classes loaded (20 events):
Event: 0.270 Loading class java/security/SignatureException done
Event: 0.270 Loading class java/security/InvalidKeyException
Event: 0.270 Loading class java/security/InvalidKeyException done
Event: 0.270 Loading class java/security/InvalidAlgorithmParameterException
Event: 0.270 Loading class java/security/InvalidAlgorithmParameterException done
Event: 0.270 Loading class java/security/interfaces/ECKey
Event: 0.270 Loading class java/security/interfaces/ECKey done
Event: 0.270 Loading class java/lang/UnsupportedOperationException
Event: 0.271 Loading class java/lang/UnsupportedOperationException done
Event: 0.271 Loading class java/security/MessageDigest
Event: 0.271 Loading class java/security/MessageDigestSpi
Event: 0.271 Loading class java/security/MessageDigestSpi done
Event: 0.271 Loading class java/security/MessageDigest done
Event: 0.271 Loading class sun/security/provider/SHA
Event: 0.272 Loading class sun/security/provider/DigestBase
Event: 0.272 Loading class sun/security/provider/DigestBase done
Event: 0.272 Loading class sun/security/provider/SHA done
Event: 0.272 Loading class sun/security/jca/GetInstance$Instance
Event: 0.272 Loading class sun/security/jca/GetInstance$Instance done
Event: 0.272 Loading class java/security/MessageDigest$Delegate

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (1 events):
Event: 0.229 Thread 0x000001f97cc0fa80 Exception <a 'sun/nio/fs/WindowsException'{0x0000000711c39ea8}> (0x0000000711c39ea8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (4 events):
Event: 0.123 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.123 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.134 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.134 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (16 events):
Event: 0.057 Thread 0x000001f97cc0fa80 Thread added: 0x000001f97cc0fa80
Event: 0.081 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abac440
Event: 0.081 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abad350
Event: 0.082 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abb1280
Event: 0.082 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abb2060
Event: 0.082 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abb2be0
Event: 0.082 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abb3ec0
Event: 0.082 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abc47c0
Event: 0.082 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91abc68d0
Event: 0.096 Thread 0x000001f91abc68d0 Thread added: 0x000001f91acff410
Event: 0.100 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91ad4d320
Event: 0.103 Thread 0x000001f97cc0fa80 Thread added: 0x000001f91ad4f370
Event: 0.108 Loaded shared library D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\net.dll
Event: 0.112 Loaded shared library D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\nio.dll
Event: 0.115 Loaded shared library D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\zip.dll
Event: 0.177 Loaded shared library D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll


Dynamic libraries:
0x00007ff634dd0000 - 0x00007ff634dda000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007ffb5a990000 - 0x00007ffb5aba7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb59690000 - 0x00007ffb59754000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb58120000 - 0x00007ffb584f2000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb58500000 - 0x00007ffb58611000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb097d0000 - 0x00007ffb097e8000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007ffb51350000 - 0x00007ffb5136b000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007ffb59cb0000 - 0x00007ffb59e61000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb57f80000 - 0x00007ffb57fa6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb5a920000 - 0x00007ffb5a949000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb57b20000 - 0x00007ffb57c43000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb57a80000 - 0x00007ffb57b1a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb45d80000 - 0x00007ffb4601b000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772\COMCTL32.dll
0x00007ffb59e80000 - 0x00007ffb59f27000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb599f0000 - 0x00007ffb59a21000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000055ef0000 - 0x0000000055efd000 	D:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffb58620000 - 0x00007ffb586d1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb59940000 - 0x00007ffb599e8000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb57f50000 - 0x00007ffb57f78000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb5a530000 - 0x00007ffb5a644000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb4fd20000 - 0x00007ffb4fe25000 	D:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffb586e0000 - 0x00007ffb58f82000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb57d10000 - 0x00007ffb57e4f000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb59270000 - 0x00007ffb59603000 	C:\WINDOWS\System32\combase.dll
0x00007ffb58fe0000 - 0x00007ffb59049000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffb574d0000 - 0x00007ffb574da000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb51340000 - 0x00007ffb5134c000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007ffa6fde0000 - 0x00007ffa6fe6d000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007ffa5dc10000 - 0x00007ffa5e9d1000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007ffb59f30000 - 0x00007ffb59fa1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb56810000 - 0x00007ffb5685d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb51710000 - 0x00007ffb51744000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb567f0000 - 0x00007ffb56803000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb56ab0000 - 0x00007ffb56ac8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb1f5e0000 - 0x00007ffb1f5ea000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007ffb54ec0000 - 0x00007ffb550f3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb59fb0000 - 0x00007ffb5a087000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb4bf60000 - 0x00007ffb4bf92000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb57ed0000 - 0x00007ffb57f4b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb097f0000 - 0x00007ffb09810000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\java.dll
0x00007ffb558f0000 - 0x00007ffb5620d000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb59ba0000 - 0x00007ffb59cac000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb579b0000 - 0x00007ffb579db000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb097b0000 - 0x00007ffb097c8000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\zip.dll
0x00007ffb0ba30000 - 0x00007ffb0ba40000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\net.dll
0x00007ffb51130000 - 0x00007ffb5125c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb56f20000 - 0x00007ffb56f89000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffb09790000 - 0x00007ffb097a6000 	D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\nio.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772;D:\Program Files (x86)\360\360Safe\safemon;D:\Program Files (x86)\jetbrains\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitee.com': 
java_class_path (initial): D:/Program Files (x86)/jetbrains/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files (x86)/jetbrains/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4257218560                                {product} {ergonomic}
   size_t MaxNewSize                               = 2554331136                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4257218560                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Java\jdk-17
CLASSPATH=.;D:\Program Files\Java\jdk-17\lib\dt.jar;D:\Program Files\Java\jdk-17\lib\tools.jar
PATH=D:/Program Files/Git/mingw64/libexec/git-core;D:/Program Files/Git/mingw64/libexec/git-core;D:\Program Files\Git\mingw64\bin;D:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\libnvvp;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\cudnn\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.6\extras\CUPTI\lib64;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\NVIDIA Corporation\Nsight Compute 2022.1.1\;D:\LenovoSoftstore\Bandizip\;D:\engine\Java\jdk1.8.0_291\bin;D:\engine\Maven\apache-maven-3.8.6\bin;D:\engine\MinGW\mingw64\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;D:\engine\NetSarang\Xshell 7\;D:\LenovoSoftstore\Tesseract-OCR;D:\Program Files\nvm;D:\Program Files\nodejs;D:\Program Files\Java\jdk-17\bin;C:\Program Files\Docker\Docker\resources\bin;D:\Program Files\apache-maven-3.9.9\bin;d:\Program Files\cursor\resources\app\bin;D:\Program Files\Git\cmd;D:\Program Files\nacos;D:\Program Files\MySQL\MySQL Server 8.4\bin;D:\Program Files\Redis\;D:\work\mcp\cunzhi;D:\Program Files\Python313\Scripts\;D:\Program Files\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\Program Files\cursor\resources\app\bin;D:\Program Files\Microsoft VS Code\bin
USERNAME=xuewen.wang
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 17, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 48648K (0% of 16624952K total physical memory with 331820K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 2211K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 44664B
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 5 days 15:34 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x50, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 16 processors :
  Max Mhz: 2304, Current Mhz: 2304, Mhz Limit: 2304

Memory: 4k page, system-wide physical 16235M (324M free)
TotalPageFile size 51523M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 47M, peak: 47M
current process commit charge ("private bytes"): 352M, peak: 353M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
