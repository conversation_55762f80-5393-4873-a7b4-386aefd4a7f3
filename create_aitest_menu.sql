-- ==========================================
-- AI测试模块动态路由创建SQL
-- 创建时间: 2025-07-28
-- 说明: 创建AI测试一级菜单和子页面，排序在其他工具之前
-- ==========================================

-- 1. 创建AI测试一级菜单 (父菜单)
INSERT INTO t_menu (
    menu_id,
    parent_id,
    menu_name,
    path,
    component,
    redirect,
    perms,
    title,
    icon,
    active_menu,
    always_show,
    hidden,
    type,
    order_mun,
    create_time,
    modify_time,
    status
) VALUES (
    36,                                          -- menu_id: 36 (下一个可用ID)
    0,                                           -- parent_id: 0 (顶级菜单)
    'AITest',                                    -- menu_name: AITest
    '/seigneur/AITest',                          -- path: 路由路径
    'Layout',                                    -- component: Layout组件
    '/seigneur/AITest/AITestManagement',         -- redirect: 默认重定向到管理页面
    NULL,                                        -- perms: 权限标识(可为空)
    'AI测试',                                    -- title: 菜单标题
    'el-icon-cpu',                               -- icon: CPU图标，符合AI测试主题
    NULL,                                        -- active_menu: 高亮菜单路径(可为空)
    1,                                           -- always_show: 1-总是显示
    NULL,                                        -- hidden: NULL-不隐藏
    0,                                           -- type: 0-菜单类型
    30,                                          -- order_mun: 30 (在其他工具99之前)
    NOW(),                                       -- create_time: 当前时间
    NOW(),                                       -- modify_time: 当前时间
    0                                            -- status: 0-正常状态
);

-- 2. 创建AI测试管理子页面
INSERT INTO t_menu (
    menu_id,
    parent_id,
    menu_name,
    path,
    component,
    redirect,
    perms,
    title,
    icon,
    active_menu,
    always_show,
    hidden,
    type,
    order_mun,
    create_time,
    modify_time,
    status
) VALUES (
    37,                                          -- menu_id: 37
    36,                                          -- parent_id: 36 (AI测试父菜单)
    'AITestManagement',                          -- menu_name: AITestManagement
    '/seigneur/AITest/AITestManagement',         -- path: 管理页面路径
    'AITestManagement',                          -- component: AITestManagement组件
    NULL,                                        -- redirect: 无重定向
    NULL,                                        -- perms: 权限标识
    '测试管理',                                  -- title: 子页面标题
    'el-icon-data-analysis',                     -- icon: 数据分析图标
    NULL,                                        -- active_menu: 高亮菜单路径
    NULL,                                        -- always_show: NULL-默认显示规则
    NULL,                                        -- hidden: NULL-不隐藏
    0,                                           -- type: 0-菜单类型
    31,                                          -- order_mun: 31
    NOW(),                                       -- create_time: 当前时间
    NOW(),                                       -- modify_time: 当前时间
    0                                            -- status: 0-正常状态
);

-- 3. 创建API测试子页面
INSERT INTO t_menu (
    menu_id,
    parent_id,
    menu_name,
    path,
    component,
    redirect,
    perms,
    title,
    icon,
    active_menu,
    always_show,
    hidden,
    type,
    order_mun,
    create_time,
    modify_time,
    status
) VALUES (
    38,                                          -- menu_id: 38
    36,                                          -- parent_id: 36 (AI测试父菜单)
    'APITest',                                   -- menu_name: APITest
    '/seigneur/AITest/APITest',                  -- path: API测试页面路径
    'APITest',                                   -- component: APITest组件
    NULL,                                        -- redirect: 无重定向
    NULL,                                        -- perms: 权限标识
    'API测试',                                   -- title: 子页面标题
    'el-icon-connection',                        -- icon: 连接图标
    NULL,                                        -- active_menu: 高亮菜单路径
    NULL,                                        -- always_show: NULL-默认显示规则
    NULL,                                        -- hidden: NULL-不隐藏
    0,                                           -- type: 0-菜单类型
    32,                                          -- order_mun: 32
    NOW(),                                       -- create_time: 当前时间
    NOW(),                                       -- modify_time: 当前时间
    0                                            -- status: 0-正常状态
);

-- 4. 创建Markdown测试子页面 (开发调试用)
INSERT INTO t_menu (
    menu_id,
    parent_id,
    menu_name,
    path,
    component,
    redirect,
    perms,
    title,
    icon,
    active_menu,
    always_show,
    hidden,
    type,
    order_mun,
    create_time,
    modify_time,
    status
) VALUES (
    39,                                          -- menu_id: 39
    36,                                          -- parent_id: 36 (AI测试父菜单)
    'MarkdownTest',                              -- menu_name: MarkdownTest
    '/seigneur/AITest/MarkdownTest',             -- path: Markdown测试页面路径
    'MarkdownTest',                              -- component: MarkdownTest组件
    NULL,                                        -- redirect: 无重定向
    NULL,                                        -- perms: 权限标识
    'Markdown测试',                              -- title: 子页面标题
    'el-icon-document',                          -- icon: 文档图标
    NULL,                                        -- active_menu: 高亮菜单路径
    NULL,                                        -- always_show: NULL-默认显示规则
    1,                                           -- hidden: 1-隐藏菜单(开发调试用)
    0,                                           -- type: 0-菜单类型
    33,                                          -- order_mun: 33
    NOW(),                                       -- create_time: 当前时间
    NOW(),                                       -- modify_time: 当前时间
    0                                            -- status: 0-正常状态
);

-- ==========================================
-- 权限分配SQL (为现有角色分配新菜单权限)
-- ==========================================

-- 5. 为管理员角色(ROLE_ADMIN, role_id=1)分配所有AI测试菜单权限
INSERT INTO t_role_menu (ROLE_ID, MENU_ID) VALUES 
(1, 36),  -- AI测试主菜单
(1, 37),  -- 测试管理
(1, 38),  -- API测试
(1, 39);  -- Markdown测试

-- 6. 为注册用户角色(ROLE_REGISTER, role_id=2)分配基础AI测试权限
INSERT INTO t_role_menu (ROLE_ID, MENU_ID) VALUES 
(2, 36),  -- AI测试主菜单
(2, 37),  -- 测试管理
(2, 38);  -- API测试
-- 注意: 注册用户不分配Markdown测试权限(开发调试功能)

-- ==========================================
-- 验证SQL (可选执行，用于验证创建结果)
-- ==========================================

-- 查看新创建的菜单
-- SELECT menu_id, parent_id, menu_name, path, title, icon, order_mun, status 
-- FROM t_menu 
-- WHERE menu_id IN (36, 37, 38, 39) 
-- ORDER BY order_mun;

-- 查看角色权限分配
-- SELECT rm.ROLE_ID, r.ROLE_NAME, rm.MENU_ID, m.menu_name, m.title 
-- FROM t_role_menu rm 
-- JOIN t_role r ON rm.ROLE_ID = r.ROLE_ID 
-- JOIN t_menu m ON rm.MENU_ID = m.menu_id 
-- WHERE rm.MENU_ID IN (36, 37, 38, 39) 
-- ORDER BY rm.ROLE_ID, rm.MENU_ID;

-- 查看完整菜单层级结构
-- SELECT 
--     CASE WHEN parent_id = 0 THEN CONCAT('├─ ', title) 
--          ELSE CONCAT('│  └─ ', title) END as menu_structure,
--     menu_id, path, order_mun
-- FROM t_menu 
-- WHERE status = 0 
-- ORDER BY 
--     CASE WHEN parent_id = 0 THEN menu_id ELSE parent_id END,
--     CASE WHEN parent_id = 0 THEN 0 ELSE 1 END,
--     order_mun;

-- ==========================================
-- 执行说明
-- ==========================================
-- 1. 执行前请确认当前最大menu_id，如有冲突请调整
-- 2. 建议先在测试环境执行验证
-- 3. 执行后需要重启前端应用以刷新路由缓存
-- 4. 如需回滚，请执行以下语句:
--    DELETE FROM t_role_menu WHERE MENU_ID IN (36, 37, 38, 39);
--    DELETE FROM t_menu WHERE menu_id IN (36, 37, 38, 39);
-- ==========================================
