<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="loading" class="detail-container">
      <div v-if="detailData" class="detail-content">
        <!-- 基础信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基础信息</span>
            </div>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="任务ID">
              {{ detailData.comparison.taskId }}
            </el-descriptions-item>
            <el-descriptions-item label="文件MD5">
              <el-tag type="info">{{ detailData.comparison.fileMd5 || '-' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(detailData.comparison.overallStatus)">
                {{ getStatusText(detailData.comparison.overallStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="评分">
              <span v-if="detailData.comparison.overallScore !== null">
                {{ detailData.comparison.overallScore }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(detailData.comparison.createTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="总耗时">
              <span v-if="detailData.comparison.totalDuration">
                {{ formatDuration(detailData.comparison.totalDuration) }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 阶段结果 -->
        <el-card class="stages-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>阶段结果</span>
            </div>
          </template>
          <div v-if="detailData.stageResults && detailData.stageResults.length > 0">
            <el-collapse v-model="activeStages" accordion>
              <el-collapse-item
                v-for="stage in detailData.stageResults"
                :key="stage.id"
                :title="getStageTitle(stage)"
                :name="stage.id"
              >
                <div class="stage-content">
                  <!-- 阶段基本信息 -->
                  <el-descriptions :column="2" border size="small">
                    <el-descriptions-item label="阶段名称">
                      {{ getStageDisplayName(stage.stageName) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                      <el-tag :type="getStageStatusTagType(stage.stageStatus)">
                        {{ getStageStatusText(stage.stageStatus) }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="AI评分">
                      <span v-if="stage.aiScore !== null">{{ stage.aiScore }}</span>
                      <span v-else class="text-gray-400">-</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="耗时">
                      <span v-if="stage.duration">{{ formatDuration(stage.duration) }}</span>
                      <span v-else class="text-gray-400">-</span>
                    </el-descriptions-item>
                  </el-descriptions>

                  <!-- 数据对比 -->
                  <div v-if="stage.uatData && stage.testData" class="data-comparison">
                    <h4>数据对比</h4>
                    <div class="diff-container">
                      <div class="diff-panel">
                        <div class="panel-header">UAT环境数据</div>
                        <div class="panel-content">
                          <pre>{{ formatJsonData(stage.uatData) }}</pre>
                        </div>
                      </div>
                      <div class="diff-panel">
                        <div class="panel-header">TEST环境数据</div>
                        <div class="panel-content">
                          <pre>{{ formatJsonData(stage.testData) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- AI评估结果 -->
                  <div v-if="stage.aiEvaluation" class="ai-evaluation">
                    <h4>AI评估结果</h4>
                    <div class="evaluation-content">
                      <div v-html="renderMarkdown(stage.aiEvaluation)"></div>
                    </div>
                  </div>

                  <!-- 错误信息 -->
                  <div v-if="stage.errorMessage" class="error-message">
                    <h4>错误信息</h4>
                    <el-alert :title="stage.errorMessage" type="error" show-icon />
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无阶段数据" />
          </div>
        </el-card>

        <!-- 整体AI评估 -->
        <el-card v-if="detailData.comparison.overallAiEvaluation" class="overall-evaluation-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>整体AI评估</span>
            </div>
          </template>
          <div class="evaluation-content">
            <div v-html="renderMarkdown(detailData.comparison.overallAiEvaluation)"></div>
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">刷新</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getAITestDetail } from '@/api/ai-test'
import { useMarkdownRenderer } from '@/utils/composables/useMarkdownRenderer'

// Props
interface Props {
  visible: boolean
  recordId: number | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  recordId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const detailData = ref<any>(null)
const activeStages = ref<string[]>([])

// Markdown渲染
const { renderMarkdown } = useMarkdownRenderer()

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  if (detailData.value?.comparison?.taskId) {
    return `任务详情 - ${detailData.value.comparison.taskId}`
  }
  return '任务详情'
})

// 监听recordId变化
watch(() => props.recordId, (newId) => {
  if (newId && props.visible) {
    fetchDetail()
  }
}, { immediate: true })

watch(() => props.visible, (visible) => {
  if (visible && props.recordId) {
    fetchDetail()
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.recordId) return

  loading.value = true
  try {
    const response = await getAITestDetail(props.recordId)
    if (response.success) {
      detailData.value = response.data
      // 默认展开第一个阶段
      if (detailData.value.stageResults?.length > 0) {
        activeStages.value = [detailData.value.stageResults[0].id.toString()]
      }
    } else {
      ElMessage.error(response.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleClose = () => {
  dialogVisible.value = false
  detailData.value = null
  activeStages.value = []
}

const handleRefresh = () => {
  fetchDetail()
  emit('refresh')
}

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    SUCCESS: 'success',
    FAILED: 'danger',
    PARTIAL_SUCCESS: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    SUCCESS: '成功',
    FAILED: '失败',
    PARTIAL_SUCCESS: '部分成功'
  }
  return textMap[status] || '未知'
}

const getStageStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    SUCCESS: 'success',
    FAILED: 'danger',
    TIMEOUT: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStageStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    SUCCESS: '成功',
    FAILED: '失败',
    TIMEOUT: '超时'
  }
  return textMap[status] || '未知'
}

const getStageDisplayName = (stageName: string) => {
  const nameMap: Record<string, string> = {
    recognize: '识别阶段',
    extraction: '提取阶段',
    structured: '结构化阶段',
    transformer: '转换阶段'
  }
  return nameMap[stageName] || stageName
}

const getStageTitle = (stage: any) => {
  const displayName = getStageDisplayName(stage.stageName)
  const statusText = getStageStatusText(stage.stageStatus)
  return `${displayName} - ${statusText}`
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatDuration = (duration: number) => {
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    return `${(duration / 60000).toFixed(1)}min`
  }
}

const formatJsonData = (data: string) => {
  try {
    const parsed = typeof data === 'string' ? JSON.parse(data) : data
    return JSON.stringify(parsed, null, 2)
  } catch {
    return data
  }
}
</script>

<style scoped>
.detail-container {
  min-height: 400px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  font-weight: 600;
  font-size: 16px;
}

.stage-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.data-comparison h4,
.ai-evaluation h4,
.error-message h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.diff-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.diff-panel {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.panel-header {
  background: #f5f7fa;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
}

.panel-content {
  padding: 12px;
  max-height: 300px;
  overflow: auto;
}

.panel-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.evaluation-content {
  padding: 12px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.text-gray-400 {
  color: #9ca3af;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
