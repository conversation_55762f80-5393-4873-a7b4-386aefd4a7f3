-- ==========================================
-- AI测试菜单验证SQL
-- 用于验证菜单修正是否成功
-- ==========================================

-- 1. 检查AI测试菜单是否存在
SELECT 
    '=== AI测试菜单信息 ===' as info;

SELECT 
    menu_id as '菜单ID',
    parent_id as '父菜单ID',
    menu_name as '菜单名称',
    path as '路由路径',
    component as '组件名称',
    title as '显示标题',
    icon as '图标',
    order_mun as '排序',
    CASE 
        WHEN hidden = 1 THEN '隐藏'
        WHEN hidden IS NULL THEN '显示'
        ELSE '显示'
    END as '是否隐藏',
    CASE 
        WHEN status = 0 THEN '正常'
        WHEN status = 1 THEN '禁用'
        ELSE '未知'
    END as '状态'
FROM t_menu 
WHERE menu_id = 36;

-- 2. 检查是否还有旧的子菜单残留
SELECT 
    '=== 检查旧菜单残留 ===' as info;

SELECT 
    menu_id as '菜单ID',
    menu_name as '菜单名称',
    title as '显示标题',
    '应该被删除' as '状态'
FROM t_menu 
WHERE menu_id IN (37, 38, 39);

-- 3. 检查角色权限分配
SELECT 
    '=== 角色权限分配 ===' as info;

SELECT 
    rm.ROLE_ID as '角色ID',
    r.ROLE_NAME as '角色名称',
    rm.MENU_ID as '菜单ID',
    m.title as '菜单标题'
FROM t_role_menu rm 
LEFT JOIN t_role r ON rm.ROLE_ID = r.ROLE_ID 
LEFT JOIN t_menu m ON rm.MENU_ID = m.menu_id 
WHERE rm.MENU_ID = 36
ORDER BY rm.ROLE_ID;

-- 4. 检查菜单在整体结构中的位置
SELECT 
    '=== 菜单层级结构 ===' as info;

SELECT 
    CASE 
        WHEN parent_id = 0 THEN CONCAT('├─ ', title) 
        ELSE CONCAT('│  └─ ', title) 
    END as '菜单结构',
    menu_id as 'ID',
    path as '路径',
    order_mun as '排序'
FROM t_menu 
WHERE status = 0 AND (parent_id = 0 OR parent_id IN (SELECT menu_id FROM t_menu WHERE parent_id = 0))
ORDER BY 
    CASE WHEN parent_id = 0 THEN menu_id ELSE parent_id END,
    CASE WHEN parent_id = 0 THEN 0 ELSE 1 END,
    order_mun;

-- 5. 检查是否有重复的menu_id
SELECT 
    '=== 检查ID冲突 ===' as info;

SELECT 
    menu_id as '菜单ID',
    COUNT(*) as '重复次数',
    GROUP_CONCAT(menu_name) as '菜单名称'
FROM t_menu 
WHERE menu_id = 36
GROUP BY menu_id
HAVING COUNT(*) > 1;

-- 6. 检查路径是否唯一
SELECT 
    '=== 检查路径冲突 ===' as info;

SELECT 
    path as '路由路径',
    COUNT(*) as '重复次数',
    GROUP_CONCAT(menu_name) as '菜单名称'
FROM t_menu 
WHERE path = '/seigneur/AITest' AND status = 0
GROUP BY path
HAVING COUNT(*) > 1;

-- 7. 最终验证结果汇总
SELECT 
    '=== 验证结果汇总 ===' as info;

SELECT 
    CASE 
        WHEN EXISTS(SELECT 1 FROM t_menu WHERE menu_id = 36 AND status = 0) 
        THEN '✅ AI测试菜单已创建'
        ELSE '❌ AI测试菜单未找到'
    END as '菜单状态',
    
    CASE 
        WHEN NOT EXISTS(SELECT 1 FROM t_menu WHERE menu_id IN (37, 38, 39)) 
        THEN '✅ 旧菜单已清理'
        ELSE '❌ 旧菜单仍存在'
    END as '清理状态',
    
    CASE 
        WHEN EXISTS(SELECT 1 FROM t_role_menu WHERE MENU_ID = 36) 
        THEN '✅ 权限已分配'
        ELSE '❌ 权限未分配'
    END as '权限状态',
    
    CASE 
        WHEN (SELECT COUNT(*) FROM t_menu WHERE menu_id = 36) = 1 
        THEN '✅ 无ID冲突'
        ELSE '❌ 存在ID冲突'
    END as 'ID状态';

-- ==========================================
-- 使用说明
-- ==========================================
-- 
-- 执行方式：
-- mysql -h localhost -P 3306 -u root -p123456 k_base < verify_aitest_menu.sql
-- 
-- 或者在MySQL客户端中：
-- source verify_aitest_menu.sql;
-- 
-- 预期结果：
-- 1. AI测试菜单信息应该显示menu_id=36的记录
-- 2. 旧菜单残留检查应该返回空结果
-- 3. 角色权限分配应该显示管理员和注册用户的权限
-- 4. 验证结果汇总应该全部显示✅
-- 
-- ==========================================
