@echo off
echo ==========================================
echo AI测试菜单修正脚本
echo ==========================================

echo 正在连接数据库...
mysql -h localhost -P 3306 -u root -p123456 k_base < update_aitest_menu.sql

if %errorlevel% equ 0 (
    echo.
    echo ✅ 菜单修正成功！
    echo.
    echo 📋 执行的操作：
    echo - 删除了旧的多级菜单结构
    echo - 创建了新的单一入口菜单
    echo - 更新了角色权限分配
    echo.
    echo 🔄 后续操作：
    echo 1. 重启前端应用以刷新路由缓存
    echo 2. 访问 /seigneur/AITest 验证功能
    echo.
    echo 验证查询：
    mysql -h localhost -P 3306 -u root -p123456 k_base -e "SELECT menu_id, menu_name, path, component, title FROM t_menu WHERE menu_id = 36;"
) else (
    echo.
    echo ❌ 菜单修正失败！
    echo 请检查：
    echo 1. MySQL服务是否运行
    echo 2. 数据库连接配置是否正确
    echo 3. 是否有足够的权限
)

echo.
echo 按任意键退出...
pause > nul
